# Y-Axis Value Discrepancy Analysis and Fixes

## Problem Description

The application was showing significant Y-axis value discrepancies between different visualization types:

1. **ToD (Time-of-Day) Generation Plot**: Showing values up to ~350,000 kWh
2. **Generation vs Consumption Plot**: Showing values up to ~30,000 kWh

Despite both plots supposedly using the same source data, the Y-axis scales were dramatically different.

## Root Causes Identified

### 1. **Different Data Sources and Granularity**
- **ToD plots** were using `get_hourly_generation_data_smart_wrapper()` with **1h granularity**
- **Generation vs Consumption plots** were using `get_generation_only_data()` with **15m granularity**
- This led to different API calls and potentially different data aggregation methods

### 2. **Inconsistent Data Filtering Logic**
- **ToD plots** were filtering out hours where either generation OR consumption was zero: 
  ```python
  valid_rows = (merged_df['generation_kwh'] > 0) & (merged_df[consumption_col] > 0)
  ```
- **Generation vs Consumption plots** used minimal filtering (only removing rows where BOTH are zero)
- This aggressive filtering in ToD plots could significantly reduce total values

### 3. **Different API Conditions**
- **ToD plots**: Used `condition={"Daily Energy": "max"}` for solar plants
- **Generation vs Consumption plots**: Used `condition={"Daily Energy": "last"}`
- This could result in different data values being retrieved

### 4. **Data Aggregation Inconsistencies**
- ToD plots aggregated hourly data directly from API
- Generation vs Consumption plots used 15-minute data and aggregated differently
- Different aggregation methods could lead to different total values

## Fixes Implemented

### 1. **Unified Data Source**
```python
# IMPORTANT: Use the same data source as Generation vs Consumption plots
# to ensure Y-axis values match between different visualizations

# Get 15-minute generation data and aggregate to hourly for ToD binning
# This ensures we use the same base data as the Generation vs Consumption plot
logger.info(f"Getting 15-minute generation data for ToD binning to ensure consistency")
minute_df = get_generation_only_data(plant_name, start_date)
```

### 2. **Consistent Filtering Logic**
```python
# IMPORTANT: Use the same filtering logic as Generation vs Consumption plots
# to ensure Y-axis values match between different visualizations

# Apply minimal filtering - only remove rows where BOTH generation AND consumption are zero
# This matches the logic in compare_generation_consumption function
valid_rows = (merged_df['generation_kwh'] > 0) | (merged_df[consumption_col] > 0)
```

### 3. **Enhanced Logging for Debugging**
Added comprehensive logging throughout the data pipeline:
```python
# Log input data for debugging Y-axis value discrepancies
logger.info(f"Generation vs Consumption - Input generation total: {gen_total:.2f} kWh")
logger.info(f"ToD - Raw data totals - Generation: {merged_df['generation_kwh'].sum():.2f} kWh")
logger.info(f"ToD - After minimal filtering - Total generation: {total_gen_after:.2f} kWh")
```

### 4. **Data Aggregation Consistency**
```python
# Aggregate 15-minute data to hourly for ToD binning
if 'quarter_hour' in minute_df.columns:
    # Create hour column from quarter_hour
    minute_df['hour'] = minute_df['quarter_hour'].astype(int)
    # Aggregate to hourly
    generation_df = minute_df.groupby('hour', as_index=False)['generation_kwh'].sum()
```

## Expected Results

After implementing these fixes:

1. **Consistent Y-axis scales** between ToD plots and Generation vs Consumption plots
2. **Same total generation values** across different visualization types
3. **Improved debugging capabilities** with detailed logging
4. **More reliable data pipeline** with unified data sources

## Testing Recommendations

1. **Compare total values** between different plot types for the same date/plant
2. **Check log outputs** to verify data consistency throughout the pipeline
3. **Verify filtering logic** is applied consistently across all visualizations
4. **Test with different plants** (both solar and wind) to ensure fixes work universally

## Monitoring

The enhanced logging will help identify any remaining discrepancies:
- Look for "Generation vs Consumption - Final output totals" log entries
- Compare with "ToD - After minimal filtering" log entries
- Values should match within reasonable rounding differences

## Future Improvements

1. **Centralized data fetching function** to ensure all visualizations use identical data sources
2. **Standardized filtering utilities** to apply consistent logic across all plot types
3. **Data validation checks** to catch discrepancies early in the pipeline
4. **Unit tests** to verify data consistency between different visualization functions
