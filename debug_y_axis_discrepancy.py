"""
Debug script to identify Y-axis discrepancies between Summary and ToD tabs.
Run this script to compare the exact data values being used in different visualizations.
"""

import pandas as pd
import datetime
from backend.logs.logger_setup import setup_logger
from backend.data.data import (
    get_generation_only_data,
    get_tod_binned_data,
    get_generation_consumption_comparison,
    compare_generation_consumption
)

# Setup logger
logger = setup_logger(__name__)

def debug_data_sources(plant_name, test_date):
    """
    Debug the data sources used by Summary vs ToD tabs to identify discrepancies.
    
    Args:
        plant_name (str): Name of the plant to test
        test_date (datetime): Date to test
    """
    print(f"\n{'='*60}")
    print(f"DEBUGGING Y-AXIS DISCREPANCY FOR {plant_name} ON {test_date}")
    print(f"{'='*60}")
    
    # 1. Test Summary Tab Generation Plot Data Source
    print(f"\n1. SUMMARY TAB - Generation Only Data:")
    print(f"   Function: get_generation_only_data()")
    try:
        summary_gen_df = get_generation_only_data(plant_name, test_date)
        if not summary_gen_df.empty:
            summary_total = summary_gen_df['generation_kwh'].sum()
            print(f"   ✓ Data retrieved: {len(summary_gen_df)} records")
            print(f"   ✓ Total Generation: {summary_total:.2f} kWh")
            print(f"   ✓ Columns: {summary_gen_df.columns.tolist()}")
            print(f"   ✓ Sample data: {summary_gen_df.head(3)[['generation_kwh']].to_dict()}")
        else:
            print(f"   ✗ No data retrieved")
            summary_total = 0
    except Exception as e:
        print(f"   ✗ Error: {e}")
        summary_total = 0
    
    # 2. Test Summary Tab Generation vs Consumption Data Source
    print(f"\n2. SUMMARY TAB - Generation vs Consumption Data:")
    print(f"   Function: get_generation_consumption_comparison() + compare_generation_consumption()")
    try:
        gen_df, cons_df = get_generation_consumption_comparison(plant_name, test_date)
        if not gen_df.empty and not cons_df.empty:
            comparison_df = compare_generation_consumption(gen_df, cons_df)
            if not comparison_df.empty:
                comparison_total = comparison_df['generation_kwh'].sum()
                print(f"   ✓ Data retrieved: {len(comparison_df)} records")
                print(f"   ✓ Total Generation: {comparison_total:.2f} kWh")
                print(f"   ✓ Columns: {comparison_df.columns.tolist()}")
            else:
                print(f"   ✗ Empty comparison dataframe")
                comparison_total = 0
        else:
            print(f"   ✗ Empty generation or consumption dataframes")
            comparison_total = 0
    except Exception as e:
        print(f"   ✗ Error: {e}")
        comparison_total = 0
    
    # 3. Test ToD Tab Data Source
    print(f"\n3. TOD TAB - ToD Binned Data:")
    print(f"   Function: get_tod_binned_data()")
    try:
        tod_df = get_tod_binned_data(plant_name, test_date)
        if not tod_df.empty and 'generation_kwh' in tod_df.columns:
            tod_total = tod_df['generation_kwh'].sum()
            print(f"   ✓ Data retrieved: {len(tod_df)} records")
            print(f"   ✓ Total Generation: {tod_total:.2f} kWh")
            print(f"   ✓ Columns: {tod_df.columns.tolist()}")
            print(f"   ✓ ToD bins: {tod_df['tod_bin'].tolist() if 'tod_bin' in tod_df.columns else 'N/A'}")
            print(f"   ✓ Generation by bin: {tod_df[['tod_bin', 'generation_kwh']].to_dict() if 'tod_bin' in tod_df.columns else 'N/A'}")
        else:
            print(f"   ✗ No data retrieved or missing generation_kwh column")
            tod_total = 0
    except Exception as e:
        print(f"   ✗ Error: {e}")
        tod_total = 0
    
    # 4. Compare Results
    print(f"\n4. COMPARISON RESULTS:")
    print(f"   Summary Generation Only:     {summary_total:.2f} kWh")
    print(f"   Summary Gen vs Consumption:  {comparison_total:.2f} kWh")
    print(f"   ToD Binned Data:             {tod_total:.2f} kWh")
    
    # Calculate discrepancies
    if summary_total > 0 and tod_total > 0:
        discrepancy_1 = abs(summary_total - tod_total)
        percentage_1 = (discrepancy_1 / summary_total) * 100
        print(f"\n   Discrepancy (Summary vs ToD): {discrepancy_1:.2f} kWh ({percentage_1:.1f}%)")
        
        if percentage_1 > 10:
            print(f"   🚨 MAJOR DISCREPANCY DETECTED! (>10%)")
        elif percentage_1 > 1:
            print(f"   ⚠️  Minor discrepancy detected (>1%)")
        else:
            print(f"   ✅ Values are consistent (<1% difference)")
    
    if comparison_total > 0 and tod_total > 0:
        discrepancy_2 = abs(comparison_total - tod_total)
        percentage_2 = (discrepancy_2 / comparison_total) * 100
        print(f"   Discrepancy (Gen vs Cons vs ToD): {discrepancy_2:.2f} kWh ({percentage_2:.1f}%)")
    
    # 5. Recommendations
    print(f"\n5. DEBUGGING RECOMMENDATIONS:")
    if summary_total > 0 and tod_total > 0:
        ratio = tod_total / summary_total
        if ratio > 5:
            print(f"   • ToD values are {ratio:.1f}x higher - check for data duplication in aggregation")
        elif ratio < 0.2:
            print(f"   • ToD values are {1/ratio:.1f}x lower - check for data loss in filtering/aggregation")
        else:
            print(f"   • Values are in reasonable range - check logs for detailed processing steps")
    
    print(f"   • Check application logs for detailed data processing information")
    print(f"   • Look for 'ToD - Total generation before/after hourly aggregation' log entries")
    print(f"   • Compare 'Generation vs Consumption - Final output totals' with ToD totals")
    
    return {
        'summary_total': summary_total,
        'comparison_total': comparison_total,
        'tod_total': tod_total,
        'major_discrepancy': (abs(summary_total - tod_total) / max(summary_total, 1)) * 100 > 10 if summary_total > 0 else False
    }

def run_debug_session():
    """Run an interactive debugging session."""
    print("Y-AXIS DISCREPANCY DEBUGGING TOOL")
    print("=" * 40)
    
    # Get user input
    plant_name = input("Enter plant name (e.g., 'Kids Clinic India Limited'): ").strip()
    if not plant_name:
        plant_name = "Kids Clinic India Limited"  # Default for testing
    
    date_str = input("Enter date (YYYY-MM-DD) or press Enter for today: ").strip()
    if not date_str:
        test_date = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    else:
        try:
            test_date = datetime.datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            print("Invalid date format. Using today's date.")
            test_date = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    
    # Run the debug
    result = debug_data_sources(plant_name, test_date)
    
    # Save results
    if result['major_discrepancy']:
        print(f"\n🚨 MAJOR DISCREPANCY DETECTED!")
        print(f"Please check the application logs and run the application to see the detailed processing steps.")
    
    return result

if __name__ == "__main__":
    # You can either run interactively or with specific parameters
    
    # Interactive mode
    run_debug_session()
    
    # Or test with specific parameters
    # test_plant = "Kids Clinic India Limited"
    # test_date = datetime.datetime(2024, 1, 15)
    # debug_data_sources(test_plant, test_date)
